/**
 * نظام إدارة الصفحة الرئيسية المحسن
 * أكاديمية 7C الرياضية
 * 
 * @version 2.0.0
 * <AUTHOR> 7C Team
 */

// إعدادات النظام
const HOMEPAGE_CONFIG = {
    apiUrl: 'api/homepage_management.php',
    autoSaveInterval: 30000, // 30 ثانية
    maxUndoSteps: 20,
    previewWindowFeatures: 'width=1200,height=800,scrollbars=yes,resizable=yes',
    colors: {
        primary: '#1a1a1a',
        secondary: '#8B4513',
        accent: '#D2691E',
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b'
    }
};

// متغيرات النظام المحسنة
let homepageData = {
    sections: [],
    settings: {
        theme: 'default',
        layout: 'modern',
        colors: HOMEPAGE_CONFIG.colors
    },
    lastSaved: null,
    version: '1.0.0'
};

let undoStack = [];
let redoStack = [];
let isAutoSaving = false;
let hasUnsavedChanges = false;
let previewWindow = null;

/**
 * دالة المعاينة المحسنة
 */
async function previewHomepage() {
    try {
        showLoadingIndicator('جاري إنشاء المعاينة...');
        
        // التحقق من وجود محتوى للمعاينة
        if (!homepageData.sections || homepageData.sections.length === 0) {
            await showAlert('تنبيه', 'لا يوجد محتوى للمعاينة. يرجى إضافة عناصر أولاً.', 'warning');
            hideLoadingIndicator();
            return;
        }

        // إنشاء HTML للمعاينة
        const previewHTML = generatePreviewHTML();
        
        // فتح نافذة المعاينة أو تحديثها
        if (previewWindow && !previewWindow.closed) {
            previewWindow.document.open();
            previewWindow.document.write(previewHTML);
            previewWindow.document.close();
            previewWindow.focus();
        } else {
            previewWindow = window.open('', '_blank', HOMEPAGE_CONFIG.previewWindowFeatures);
            previewWindow.document.write(previewHTML);
            previewWindow.document.close();
        }

        hideLoadingIndicator();
        
        // تسجيل العملية
        console.log('✅ تم إنشاء معاينة الصفحة الرئيسية بنجاح');
        
        await showAlert('نجح', 'تم فتح معاينة الصفحة الرئيسية في نافذة جديدة', 'success');
        
    } catch (error) {
        console.error('❌ خطأ في معاينة الصفحة:', error);
        hideLoadingIndicator();
        await showAlert('خطأ', 'حدث خطأ أثناء إنشاء المعاينة: ' + error.message, 'error');
    }
}

/**
 * دالة النشر المحسنة مع قاعدة البيانات
 */
async function publishHomepage() {
    try {
        // التحقق من وجود تغييرات غير محفوظة
        if (hasUnsavedChanges) {
            const saveFirst = await showConfirmDialog(
                'تغييرات غير محفوظة',
                'يوجد تغييرات غير محفوظة. هل تريد حفظها قبل النشر؟',
                'question'
            );
            
            if (saveFirst.isConfirmed) {
                await saveHomepage();
            } else {
                return;
            }
        }

        // تأكيد النشر
        const confirmPublish = await showConfirmDialog(
            'تأكيد النشر',
            'هل أنت متأكد من نشر الصفحة الرئيسية؟ سيتم استبدال الصفحة الحالية على الموقع.',
            'question'
        );

        if (!confirmPublish.isConfirmed) {
            return;
        }

        showLoadingIndicator('جاري نشر الصفحة الرئيسية...');

        // إرسال طلب النشر إلى الخادم
        const response = await fetch(HOMEPAGE_CONFIG.apiUrl, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json; charset=utf-8'
            },
            body: JSON.stringify({
                action: 'publish_homepage',
                data: homepageData
            })
        });

        const result = await response.json();

        if (result.success) {
            // تحديث معلومات النشر
            homepageData.lastPublished = new Date().toISOString();
            homepageData.publishedVersion = result.data.version;
            
            // حفظ في localStorage
            localStorage.setItem('homepage_data', JSON.stringify(homepageData));
            
            hideLoadingIndicator();
            
            await showAlert(
                'تم النشر بنجاح! 🚀',
                `تم نشر الصفحة الرئيسية بنجاح\nالإصدار: ${result.data.version}\nوقت النشر: ${result.data.published_at}`,
                'success'
            );
            
            // تحديث مؤشر الحفظ
            updateSaveIndicator('published');
            
            console.log('✅ تم نشر الصفحة الرئيسية بنجاح:', result.data);
            
        } else {
            throw new Error(result.message || 'فشل في نشر الصفحة');
        }

    } catch (error) {
        console.error('❌ خطأ في نشر الصفحة:', error);
        hideLoadingIndicator();
        
        await showAlert(
            'خطأ في النشر',
            'حدث خطأ أثناء نشر الصفحة الرئيسية: ' + error.message,
            'error'
        );
    }
}

/**
 * دالة التراجع المحسنة
 */
async function undoAction() {
    try {
        if (undoStack.length <= 1) {
            await showAlert('تنبيه', 'لا توجد إجراءات للتراجع عنها', 'info');
            return;
        }

        showLoadingIndicator('جاري التراجع...');

        // حفظ الحالة الحالية في مكدس الإعادة
        redoStack.push(undoStack.pop());

        // استرجاع الحالة السابقة
        const previousState = undoStack[undoStack.length - 1];
        homepageData = JSON.parse(previousState);
        
        // إعادة رسم الصفحة
        await renderHomepage();
        updateSaveIndicator('unsaved');
        hasUnsavedChanges = true;

        hideLoadingIndicator();
        
        console.log('✅ تم التراجع عن آخر إجراء');
        
        // إشعار بصري
        showToast('تم التراجع عن آخر إجراء', 'info');

    } catch (error) {
        console.error('❌ خطأ في التراجع:', error);
        hideLoadingIndicator();
        await showAlert('خطأ', 'حدث خطأ أثناء التراجع: ' + error.message, 'error');
    }
}

/**
 * دالة الإعادة المحسنة
 */
async function redoAction() {
    try {
        if (redoStack.length === 0) {
            await showAlert('تنبيه', 'لا توجد إجراءات للإعادة', 'info');
            return;
        }

        showLoadingIndicator('جاري الإعادة...');

        // استرجاع الحالة التالية
        const nextState = redoStack.pop();
        undoStack.push(nextState);
        
        homepageData = JSON.parse(nextState);
        
        // إعادة رسم الصفحة
        await renderHomepage();
        updateSaveIndicator('unsaved');
        hasUnsavedChanges = true;

        hideLoadingIndicator();
        
        console.log('✅ تم إعادة آخر إجراء');
        
        // إشعار بصري
        showToast('تم إعادة آخر إجراء', 'success');

    } catch (error) {
        console.error('❌ خطأ في الإعادة:', error);
        hideLoadingIndicator();
        await showAlert('خطأ', 'حدث خطأ أثناء الإعادة: ' + error.message, 'error');
    }
}

/**
 * دالة الحفظ المحسنة مع قاعدة البيانات
 */
async function saveHomepage() {
    try {
        showLoadingIndicator('جاري حفظ البيانات...');

        // تحديث وقت الحفظ
        homepageData.lastSaved = new Date().toISOString();

        // حفظ في localStorage أولاً
        localStorage.setItem('homepage_data', JSON.stringify(homepageData));

        // إرسال إلى قاعدة البيانات
        const response = await fetch(HOMEPAGE_CONFIG.apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=utf-8'
            },
            body: JSON.stringify({
                action: 'save_homepage',
                data: homepageData,
                version: homepageData.version,
                status: 'draft'
            })
        });

        const result = await response.json();

        if (result.success) {
            hasUnsavedChanges = false;
            updateSaveIndicator('saved');
            
            // تحديث معلومات آخر حفظ
            const lastSaveElement = document.getElementById('last-save-time');
            if (lastSaveElement) {
                lastSaveElement.textContent = new Date().toLocaleTimeString('ar-SA');
            }

            hideLoadingIndicator();
            
            console.log('✅ تم حفظ البيانات بنجاح:', result.data);
            
            showToast('تم حفظ التغييرات بنجاح', 'success');
            
        } else {
            throw new Error(result.message || 'فشل في حفظ البيانات');
        }

    } catch (error) {
        console.error('❌ خطأ في حفظ البيانات:', error);
        hideLoadingIndicator();
        
        // الحفظ في localStorage كنسخة احتياطية
        try {
            localStorage.setItem('homepage_data_backup', JSON.stringify(homepageData));
            await showAlert(
                'تحذير',
                'فشل الحفظ في قاعدة البيانات، تم حفظ نسخة احتياطية محلياً.\nالخطأ: ' + error.message,
                'warning'
            );
        } catch (localError) {
            await showAlert('خطأ', 'فشل في حفظ البيانات: ' + error.message, 'error');
        }
    }
}

/**
 * دالة رسم الصفحة الرئيسية المحسنة
 */
async function renderHomepage() {
    try {
        const canvas = document.getElementById('page-canvas');
        const emptyCanvas = document.getElementById('empty-canvas');

        if (!canvas) {
            console.warn('⚠️ عنصر page-canvas غير موجود');
            return;
        }

        // مسح المحتوى الحالي
        canvas.innerHTML = '';

        if (!homepageData.sections || homepageData.sections.length === 0) {
            // عرض رسالة الصفحة الفارغة
            if (emptyCanvas) {
                canvas.appendChild(emptyCanvas);
                emptyCanvas.style.display = 'block';
            }
        } else {
            // إخفاء رسالة الصفحة الفارغة
            if (emptyCanvas) {
                emptyCanvas.style.display = 'none';
            }

            // رسم العناصر
            homepageData.sections.forEach(sectionData => {
                const elementDOM = createElementDOM(sectionData);
                canvas.appendChild(elementDOM);
            });
        }

        console.log('✅ تم رسم الصفحة الرئيسية بنجاح');

    } catch (error) {
        console.error('❌ خطأ في رسم الصفحة:', error);
        await showAlert('خطأ', 'حدث خطأ أثناء رسم الصفحة: ' + error.message, 'error');
    }
}

/**
 * دوال مساعدة محسنة
 */

// إنشاء HTML للمعاينة
function generatePreviewHTML() {
    const canvas = document.getElementById('page-canvas');
    const canvasContent = canvas ? canvas.innerHTML : '<p style="text-align: center; color: #666;">لا يوجد محتوى للمعاينة</p>';
    
    return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>معاينة الصفحة الرئيسية - أكاديمية 7C الرياضية</title>
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body { 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, ${HOMEPAGE_CONFIG.colors.primary}, ${HOMEPAGE_CONFIG.colors.secondary});
                    color: white;
                    min-height: 100vh;
                    padding: 20px;
                }
                .preview-header {
                    background: rgba(255,255,255,0.1);
                    padding: 15px;
                    border-radius: 10px;
                    margin-bottom: 20px;
                    text-align: center;
                    backdrop-filter: blur(10px);
                }
                .preview-content {
                    background: rgba(255,255,255,0.05);
                    border-radius: 15px;
                    padding: 20px;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.1);
                }
                .element { margin: 10px 0; padding: 15px; border-radius: 8px; }
                .close-btn {
                    position: fixed;
                    top: 20px;
                    left: 20px;
                    background: ${HOMEPAGE_CONFIG.colors.error};
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    cursor: pointer;
                    z-index: 1000;
                }
            </style>
        </head>
        <body>
            <button class="close-btn" onclick="window.close()">✕ إغلاق المعاينة</button>
            <div class="preview-header">
                <h1>🏠 معاينة الصفحة الرئيسية</h1>
                <p>أكاديمية 7C الرياضية - ${new Date().toLocaleString('ar-SA')}</p>
            </div>
            <div class="preview-content">
                ${canvasContent}
            </div>
        </body>
        </html>
    `;
}

// حفظ الحالة الحالية
function saveState() {
    const state = JSON.stringify(homepageData);
    undoStack.push(state);

    // الحد الأقصى للخطوات
    if (undoStack.length > HOMEPAGE_CONFIG.maxUndoSteps) {
        undoStack.shift();
    }

    // مسح مكدس الإعادة عند حفظ حالة جديدة
    redoStack = [];
}

// تحديث مؤشر الحفظ
function updateSaveIndicator(status) {
    const indicator = document.getElementById('save-indicator');
    if (!indicator) return;

    indicator.className = 'save-indicator';

    switch (status) {
        case 'saved':
            indicator.classList.add('bg-green-500');
            indicator.textContent = 'محفوظ';
            break;
        case 'unsaved':
            indicator.classList.add('bg-orange-500');
            indicator.textContent = 'غير محفوظ';
            hasUnsavedChanges = true;
            break;
        case 'saving':
            indicator.classList.add('bg-blue-500');
            indicator.textContent = 'جاري الحفظ...';
            break;
        case 'published':
            indicator.classList.add('bg-purple-500');
            indicator.textContent = 'منشور';
            break;
        case 'error':
            indicator.classList.add('bg-red-500');
            indicator.textContent = 'خطأ';
            break;
    }
}

/**
 * دوال الواجهة والتفاعل المحسنة
 */

// عرض مؤشر التحميل
function showLoadingIndicator(message = 'جاري التحميل...') {
    const existingLoader = document.getElementById('loading-indicator');
    if (existingLoader) {
        existingLoader.remove();
    }

    const loader = document.createElement('div');
    loader.id = 'loading-indicator';
    loader.innerHTML = `
        <div style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(5px);
        ">
            <div style="
                background: white;
                padding: 30px;
                border-radius: 15px;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                color: ${HOMEPAGE_CONFIG.colors.primary};
            ">
                <div style="
                    width: 40px;
                    height: 40px;
                    border: 4px solid ${HOMEPAGE_CONFIG.colors.secondary};
                    border-top: 4px solid ${HOMEPAGE_CONFIG.colors.accent};
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 15px;
                "></div>
                <p style="margin: 0; font-weight: bold;">${message}</p>
            </div>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;

    document.body.appendChild(loader);
}

// إخفاء مؤشر التحميل
function hideLoadingIndicator() {
    const loader = document.getElementById('loading-indicator');
    if (loader) {
        loader.remove();
    }
}

// عرض رسائل SweetAlert2 محسنة
async function showAlert(title, text, icon = 'info') {
    if (typeof Swal === 'undefined') {
        alert(`${title}\n${text}`);
        return;
    }

    return await Swal.fire({
        title: title,
        text: text,
        icon: icon,
        confirmButtonText: 'موافق',
        background: 'var(--glass-strong, #1a1a1a)',
        color: 'var(--text-primary, white)',
        confirmButtonColor: HOMEPAGE_CONFIG.colors.secondary,
        customClass: {
            popup: 'rtl-popup',
            title: 'rtl-title',
            content: 'rtl-content'
        }
    });
}

// عرض حوار التأكيد
async function showConfirmDialog(title, text, icon = 'question') {
    if (typeof Swal === 'undefined') {
        return { isConfirmed: confirm(`${title}\n${text}`) };
    }

    return await Swal.fire({
        title: title,
        text: text,
        icon: icon,
        showCancelButton: true,
        confirmButtonText: 'نعم',
        cancelButtonText: 'إلغاء',
        background: 'var(--glass-strong, #1a1a1a)',
        color: 'var(--text-primary, white)',
        confirmButtonColor: HOMEPAGE_CONFIG.colors.secondary,
        cancelButtonColor: HOMEPAGE_CONFIG.colors.error,
        customClass: {
            popup: 'rtl-popup',
            title: 'rtl-title',
            content: 'rtl-content'
        }
    });
}

// عرض إشعار سريع (Toast)
function showToast(message, type = 'info', duration = 3000) {
    const existingToast = document.getElementById('toast-notification');
    if (existingToast) {
        existingToast.remove();
    }

    const colors = {
        success: HOMEPAGE_CONFIG.colors.success,
        error: HOMEPAGE_CONFIG.colors.error,
        warning: HOMEPAGE_CONFIG.colors.warning,
        info: HOMEPAGE_CONFIG.colors.secondary
    };

    const toast = document.createElement('div');
    toast.id = 'toast-notification';
    toast.innerHTML = `
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${colors[type] || colors.info};
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            z-index: 10000;
            animation: slideIn 0.3s ease-out;
            max-width: 300px;
            direction: rtl;
        ">
            ${message}
        </div>
        <style>
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        </style>
    `;

    document.body.appendChild(toast);

    // إزالة الإشعار تلقائياً
    setTimeout(() => {
        if (toast) {
            toast.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => toast.remove(), 300);
        }
    }, duration);
}

/**
 * تحميل البيانات من قاعدة البيانات
 */
async function loadHomepageData() {
    try {
        showLoadingIndicator('جاري تحميل بيانات الصفحة الرئيسية...');

        const response = await fetch(`${HOMEPAGE_CONFIG.apiUrl}?action=get_homepage`);
        const result = await response.json();

        if (result.success && result.data.homepage) {
            homepageData = result.data.homepage;

            // تحديث معلومات آخر حفظ
            if (result.data.last_updated) {
                const lastSaveElement = document.getElementById('last-save-time');
                if (lastSaveElement) {
                    const saveDate = new Date(result.data.last_updated);
                    lastSaveElement.textContent = saveDate.toLocaleTimeString('ar-SA');
                }
            }

            await renderHomepage();
            updateSaveIndicator('saved');
            hasUnsavedChanges = false;

            console.log('✅ تم تحميل بيانات الصفحة الرئيسية من قاعدة البيانات');

        } else {
            // تحميل من localStorage كنسخة احتياطية
            const localData = localStorage.getItem('homepage_data');
            if (localData) {
                homepageData = JSON.parse(localData);
                await renderHomepage();
                console.log('⚠️ تم تحميل البيانات من التخزين المحلي');
                showToast('تم تحميل البيانات من التخزين المحلي', 'warning');
            }
        }

        hideLoadingIndicator();

    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
        hideLoadingIndicator();

        // محاولة التحميل من localStorage
        try {
            const localData = localStorage.getItem('homepage_data');
            if (localData) {
                homepageData = JSON.parse(localData);
                await renderHomepage();
                showToast('تم تحميل البيانات من التخزين المحلي', 'warning');
            }
        } catch (localError) {
            await showAlert('خطأ', 'فشل في تحميل بيانات الصفحة الرئيسية', 'error');
        }
    }
}

/**
 * الحفظ التلقائي المحسن
 */
function setupAutoSave() {
    setInterval(async () => {
        if (hasUnsavedChanges && !isAutoSaving) {
            isAutoSaving = true;
            try {
                await saveHomepage();
                console.log('💾 تم الحفظ التلقائي');
            } catch (error) {
                console.error('❌ خطأ في الحفظ التلقائي:', error);
            } finally {
                isAutoSaving = false;
            }
        }
    }, HOMEPAGE_CONFIG.autoSaveInterval);
}

/**
 * إعداد اختصارات لوحة المفاتيح المحسنة
 */
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+Z - التراجع
        if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
            e.preventDefault();
            undoAction();
        }

        // Ctrl+Y أو Ctrl+Shift+Z - الإعادة
        if (e.ctrlKey && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {
            e.preventDefault();
            redoAction();
        }

        // Ctrl+S - الحفظ
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveHomepage();
        }

        // Ctrl+P - المعاينة
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            previewHomepage();
        }

        // Ctrl+Shift+P - النشر
        if (e.ctrlKey && e.shiftKey && e.key === 'P') {
            e.preventDefault();
            publishHomepage();
        }
    });
}

/**
 * تهيئة النظام عند تحميل الصفحة
 */
document.addEventListener('DOMContentLoaded', async function() {
    try {
        console.log('🚀 بدء تهيئة نظام إدارة الصفحة الرئيسية المحسن...');

        await loadHomepageData();
        setupKeyboardShortcuts();
        setupAutoSave();
        updateSaveIndicator('saved');

        // حفظ الحالة الأولية
        saveState();

        console.log('✅ تم تحميل نظام إدارة الصفحة الرئيسية المحسن بالكامل');
        showToast('تم تحميل النظام بنجاح', 'success');

    } catch (error) {
        console.error('❌ خطأ في تهيئة النظام:', error);
        showToast('خطأ في تهيئة النظام', 'error');
    }
});

// منع فقدان البيانات عند إغلاق الصفحة
window.addEventListener('beforeunload', function(e) {
    if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = 'يوجد تغييرات غير محفوظة. هل أنت متأكد من الخروج؟';
        return e.returnValue;
    }
});
