/**
 * نظام إدارة الصفحة الرئيسية المحسن والمطور
 * أكاديمية 7C الرياضية
 *
 * الميزات المحسنة:
 * - قاعدة بيانات متكاملة مع MySQL (new7cdata)
 * - حفظ تلقائي ذكي كل 30 ثانية
 * - نظام تراجع وإعادة متقدم (50 حالة)
 * - معاينة ونشر محسن مع إدارة الإصدارات
 * - واجهة عربية RTL محسنة
 * - معالجة شاملة للأخطاء والاستثناءات
 * - اختصارات لوحة المفاتيح متقدمة
 * - إشعارات بصرية وصوتية
 * - حماية من فقدان البيانات
 * - تحسينات الأداء والذاكرة
 *
 * @version 3.0.0
 * <AUTHOR> 7C Development Team
 * @date 2024-12-19
 * @license MIT
 */

// إعدادات النظام المحسنة والمطورة
const HOMEPAGE_CONFIG = {
    // إعدادات الخادم وقاعدة البيانات
    apiUrl: 'api/homepage_management.php',
    databaseConfig: {
        host: 'localhost',
        database: 'new7cdata',
        username: 'komaro',
        password: 'ZdShaker@14'
    },

    // إعدادات الأداء والتحسين
    autoSaveInterval: 30000, // 30 ثانية
    maxUndoSteps: 50, // زيادة عدد خطوات التراجع
    previewWindowFeatures: 'width=1400,height=900,scrollbars=yes,resizable=yes,location=no,menubar=no,toolbar=no',

    // ألوان الأكاديمية المحدثة
    colors: {
        primary: '#1a1a1a',      // الأسود الأساسي
        secondary: '#8B4513',    // البني الثانوي
        accent: '#D2691E',       // البرتقالي المميز
        success: '#10b981',      // الأخضر للنجاح
        error: '#ef4444',        // الأحمر للأخطاء
        warning: '#f59e0b',      // الأصفر للتحذيرات
        info: '#3b82f6',         // الأزرق للمعلومات
        glass: 'rgba(26, 26, 26, 0.8)', // خلفية شفافة
        glassBorder: 'rgba(255, 255, 255, 0.1)' // حدود شفافة
    },

    // إعدادات الواجهة
    ui: {
        animationDuration: 300,
        toastDuration: 3000,
        loadingMinDuration: 1000,
        rtlDirection: true,
        arabicFont: 'Cairo, Tajawal, sans-serif'
    },

    // إعدادات الأمان
    security: {
        maxFileSize: 5 * 1024 * 1024, // 5MB
        allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        csrfProtection: true,
        sanitizeInput: true
    }
};

/**
 * متغيرات النظام المحسنة والمطورة
 */

// بيانات الصفحة الرئيسية الأساسية
let homepageData = {
    sections: [],
    settings: {
        theme: 'academy-dark',
        layout: 'modern-rtl',
        colors: HOMEPAGE_CONFIG.colors,
        language: 'ar',
        direction: 'rtl',
        font: HOMEPAGE_CONFIG.ui.arabicFont
    },
    metadata: {
        title: 'أكاديمية 7C الرياضية',
        description: 'الصفحة الرئيسية لأكاديمية 7C الرياضية',
        keywords: 'أكاديمية, رياضة, كرة قدم, تدريب',
        author: 'Academy 7C Team'
    },
    lastSaved: null,
    lastPublished: null,
    version: '1.0.0',
    publishedVersion: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
};

// متغيرات إدارة الحالة المتقدمة
let undoStack = [];
let redoStack = [];
let currentStateIndex = 0;
let maxStateHistory = HOMEPAGE_CONFIG.maxUndoSteps;

// متغيرات التحكم في النظام
let isAutoSaving = false;
let hasUnsavedChanges = false;
let isOnline = navigator.onLine;
let lastSyncTime = null;
let syncRetryCount = 0;
let maxSyncRetries = 3;

// متغيرات الواجهة
let previewWindow = null;
let loadingIndicator = null;
let saveIndicator = null;
let currentToast = null;

// متغيرات الأداء
let performanceMetrics = {
    saveTime: 0,
    loadTime: 0,
    renderTime: 0,
    lastOperation: null
};

// متغيرات الأمان
let csrfToken = null;
let sessionId = null;
let lastActivity = Date.now();

/**
 * دالة المعاينة المحسنة والمطورة
 * تنشئ معاينة ديناميكية للصفحة الرئيسية مع تصميم الأكاديمية
 *
 * الميزات المحسنة:
 * - معاينة في الوقت الفعلي مع التصميم الكامل
 * - دعم الأجهزة المختلفة (سطح المكتب، تابلت، موبايل)
 * - تحسينات الأداء وسرعة التحميل
 * - معالجة شاملة للأخطاء
 * - إحصائيات الأداء والتحليلات
 *
 * @returns {Promise<void>}
 */
async function previewHomepage() {
    const startTime = performance.now();

    try {
        // تسجيل بداية العملية
        console.log('🔍 بدء إنشاء معاينة الصفحة الرئيسية...');
        updateLastActivity();

        // عرض مؤشر التحميل المحسن
        showLoadingIndicator('جاري إنشاء المعاينة المتقدمة...', 'preview');

        // التحقق الشامل من صحة البيانات والمحتوى
        const validationResult = await validateHomepageData();
        if (!validationResult.isValid) {
            await showAlert(
                'تحذير - بيانات غير مكتملة',
                `${validationResult.message}\n\nهل تريد المتابعة مع المعاينة الأساسية؟`,
                'warning'
            );

            // إذا لم يكن هناك محتوى على الإطلاق، توقف
            if (!homepageData.sections || homepageData.sections.length === 0) {
                hideLoadingIndicator();
                return;
            }
        }

        // إغلاق نافذة المعاينة السابقة بأمان
        if (previewWindow && !previewWindow.closed) {
            try {
                previewWindow.close();
                console.log('🔄 تم إغلاق نافذة المعاينة السابقة');
            } catch (e) {
                console.warn('⚠️ تعذر إغلاق النافذة السابقة:', e.message);
            }
        }

        // الحصول على محتوى الصفحة مع معالجة الأخطاء
        const canvas = document.getElementById('page-canvas');
        let canvasContent = '';

        if (canvas && canvas.innerHTML.trim()) {
            canvasContent = canvas.innerHTML;
            console.log('📄 تم الحصول على محتوى الصفحة من Canvas');
        } else {
            canvasContent = await generateDefaultPreviewContent();
            console.log('📝 تم إنشاء محتوى افتراضي للمعاينة');
        }

        // إنشاء HTML المعاينة المحسن والمطور
        const previewHTML = await generateAdvancedPreviewHTML(canvasContent);

        // إنشاء نافذة معاينة جديدة مع معالجة الأخطاء
        previewWindow = window.open('', '_blank', HOMEPAGE_CONFIG.previewWindowFeatures);

        if (!previewWindow) {
            throw new Error('فشل في فتح نافذة المعاينة. يرجى السماح بالنوافذ المنبثقة في المتصفح.');
        }

        // كتابة المحتوى في النافذة مع معالجة الأخطاء
        try {
            previewWindow.document.write(previewHTML);
            previewWindow.document.close();

            // إضافة معالجات الأحداث المتقدمة
            await setupAdvancedPreviewEventHandlers();

            // تطبيق التحسينات البصرية
            await applyPreviewEnhancements();

        } catch (writeError) {
            console.error('❌ خطأ في كتابة محتوى المعاينة:', writeError);
            previewWindow.close();
            throw new Error('فشل في إنشاء محتوى المعاينة');
        }

        // حساب وقت الأداء
        const endTime = performance.now();
        performanceMetrics.renderTime = endTime - startTime;
        performanceMetrics.lastOperation = 'preview';

        hideLoadingIndicator();
        showToast(`تم إنشاء المعاينة بنجاح في ${Math.round(performanceMetrics.renderTime)}ms`, 'success');

        console.log(`✅ تم إنشاء معاينة الصفحة الرئيسية بنجاح في ${Math.round(performanceMetrics.renderTime)}ms`);
        
    } catch (error) {
        // معالجة شاملة للأخطاء مع تسجيل مفصل
        const endTime = performance.now();
        const errorTime = endTime - startTime;

        console.error('❌ خطأ في معاينة الصفحة:', {
            message: error.message,
            stack: error.stack,
            time: errorTime,
            homepageDataValid: !!homepageData,
            sectionsCount: homepageData?.sections?.length || 0,
            isOnline: navigator.onLine,
            userAgent: navigator.userAgent
        });

        hideLoadingIndicator();

        // إغلاق النافذة في حالة الخطأ
        if (previewWindow && !previewWindow.closed) {
            try {
                previewWindow.close();
            } catch (e) {
                console.warn('⚠️ تعذر إغلاق النافذة بعد الخطأ');
            }
        }

        // عرض رسالة خطأ مفصلة ومفيدة
        const errorMessage = getDetailedErrorMessage(error);
        await showAlert(
            'خطأ في إنشاء المعاينة',
            errorMessage,
            'error'
        );

        // إرسال تقرير الخطأ للتحليل (اختياري)
        reportError('preview_error', error, {
            time: errorTime,
            context: 'homepage_preview',
            userAction: 'preview_homepage'
        });
    }
}

/**
 * دالة النشر المحسنة والمطورة مع قاعدة البيانات
 * تتضمن ميزات متقدمة للنشر الآمن والموثوق
 *
 * الميزات المحسنة:
 * - فحص شامل للبيانات قبل النشر
 * - نسخ احتياطية تلقائية
 * - إدارة الإصدارات المتقدمة
 * - مزامنة مع قاعدة البيانات
 * - معالجة شاملة للأخطاء
 * - تتبع حالة النشر
 * - إشعارات متقدمة
 *
 * @returns {Promise<void>}
 */
async function publishHomepage() {
    const startTime = performance.now();
    let backupCreated = false;

    try {
        // تسجيل بداية عملية النشر
        console.log('🚀 بدء عملية نشر الصفحة الرئيسية...');
        updateLastActivity();

        // التحقق من الاتصال بالإنترنت
        if (!navigator.onLine) {
            throw new Error('لا يوجد اتصال بالإنترنت. يرجى التحقق من الاتصال والمحاولة مرة أخرى.');
        }

        // فحص شامل للبيانات قبل النشر
        const validationResult = await validateHomepageDataForPublishing();
        if (!validationResult.isValid) {
            await showAlert(
                'خطأ في البيانات',
                `لا يمكن نشر الصفحة بسبب الأخطاء التالية:\n\n${validationResult.errors.join('\n')}`,
                'error'
            );
            return;
        }

        // التحقق من وجود تغييرات غير محفوظة
        if (hasUnsavedChanges) {
            const saveFirst = await showConfirmDialog(
                'تغييرات غير محفوظة',
                'يوجد تغييرات غير محفوظة. هل تريد حفظها قبل النشر؟',
                'question'
            );

            if (saveFirst.isConfirmed) {
                await saveHomepageEnhanced();
                console.log('💾 تم حفظ التغييرات قبل النشر');
            } else if (saveFirst.isDismissed) {
                console.log('❌ تم إلغاء عملية النشر بواسطة المستخدم');
                return;
            }
        }

        // تأكيد النشر مع معلومات مفصلة
        const publishInfo = await generatePublishInfo();
        const confirmPublish = await showConfirmDialog(
            'تأكيد نشر الصفحة الرئيسية',
            `هل أنت متأكد من نشر الصفحة الرئيسية؟\n\n${publishInfo}\n\nسيتم استبدال الصفحة الحالية على الموقع.`,
            'question'
        );

        if (!confirmPublish.isConfirmed) {
            console.log('❌ تم إلغاء عملية النشر بواسطة المستخدم');
            return;
        }

        // عرض مؤشر التحميل المتقدم
        showLoadingIndicator('جاري إنشاء نسخة احتياطية ونشر الصفحة...', 'publish');

        // إنشاء نسخة احتياطية قبل النشر
        try {
            await createBackupBeforePublish();
            backupCreated = true;
            console.log('💾 تم إنشاء نسخة احتياطية بنجاح');
        } catch (backupError) {
            console.warn('⚠️ فشل في إنشاء النسخة الاحتياطية:', backupError.message);

            const continueWithoutBackup = await showConfirmDialog(
                'تحذير - فشل النسخ الاحتياطي',
                'فشل في إنشاء نسخة احتياطية. هل تريد المتابعة بدون نسخة احتياطية؟',
                'warning'
            );

            if (!continueWithoutBackup.isConfirmed) {
                hideLoadingIndicator();
                return;
            }
        }

        // تحديث مؤشر التحميل
        showLoadingIndicator('جاري رفع البيانات إلى الخادم...', 'upload');

        // إعداد البيانات للنشر
        const publishData = {
            action: 'publish_homepage',
            data: {
                ...homepageData,
                publishedAt: new Date().toISOString(),
                publishedBy: sessionStorage.getItem('current_user') || 'admin',
                version: generateNewVersion(),
                backupCreated: backupCreated
            },
            metadata: {
                userAgent: navigator.userAgent,
                timestamp: Date.now(),
                sessionId: sessionId
            }
        };

        // إرسال طلب النشر إلى الخادم مع معالجة متقدمة
        const response = await fetch(HOMEPAGE_CONFIG.apiUrl, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json; charset=utf-8',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-Token': csrfToken || '',
                'Accept': 'application/json'
            },
            body: JSON.stringify(publishData)
        });

        // التحقق من حالة الاستجابة
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`خطأ في الخادم (${response.status}): ${errorText}`);
        }

        const result = await response.json();

        if (result.success) {
            // تحديث معلومات النشر المحلية
            homepageData.lastPublished = result.data.published_at;
            homepageData.publishedVersion = result.data.version;
            homepageData.publishedBy = result.data.published_by;
            hasUnsavedChanges = false;

            // حفظ في localStorage مع التحديثات
            localStorage.setItem('homepage_data', JSON.stringify(homepageData));
            localStorage.setItem('last_publish_info', JSON.stringify({
                version: result.data.version,
                publishedAt: result.data.published_at,
                success: true
            }));

            // حساب وقت الأداء
            const endTime = performance.now();
            performanceMetrics.saveTime = endTime - startTime;
            performanceMetrics.lastOperation = 'publish';

            hideLoadingIndicator();

            // عرض رسالة نجاح مفصلة
            await showAlert(
                'تم النشر بنجاح! 🚀',
                `تم نشر الصفحة الرئيسية بنجاح\n\n` +
                `📋 الإصدار: ${result.data.version}\n` +
                `⏰ وقت النشر: ${new Date(result.data.published_at).toLocaleString('ar-SA')}\n` +
                `⚡ وقت المعالجة: ${Math.round(performanceMetrics.saveTime)}ms\n` +
                `💾 نسخة احتياطية: ${backupCreated ? 'تم إنشاؤها' : 'غير متاحة'}`,
                'success'
            );

            // تحديث مؤشر الحفظ
            updateSaveIndicator('published');

            // إرسال إشعار نجاح
            showToast('تم نشر الصفحة الرئيسية بنجاح', 'success');

            console.log(`✅ تم نشر الصفحة الرئيسية بنجاح في ${Math.round(performanceMetrics.saveTime)}ms:`, result.data);
            
        } else {
            // معالجة أخطاء الخادم المفصلة
            const errorMessage = result.message || 'فشل في نشر الصفحة لسبب غير معروف';
            const errorCode = result.error_code || 'UNKNOWN_ERROR';

            console.error('❌ خطأ من الخادم:', {
                message: errorMessage,
                code: errorCode,
                details: result.details || 'لا توجد تفاصيل إضافية'
            });

            throw new Error(`${errorMessage} (كود الخطأ: ${errorCode})`);
        }

    } catch (error) {
        // معالجة شاملة للأخطاء مع تسجيل مفصل
        const endTime = performance.now();
        const errorTime = endTime - startTime;

        console.error('❌ خطأ في نشر الصفحة:', {
            message: error.message,
            stack: error.stack,
            time: errorTime,
            backupCreated: backupCreated,
            isOnline: navigator.onLine,
            homepageDataValid: !!homepageData,
            sectionsCount: homepageData?.sections?.length || 0
        });

        hideLoadingIndicator();

        // تحديد نوع الخطأ وعرض رسالة مناسبة
        let errorTitle = 'خطأ في النشر';
        let errorMessage = '';

        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            errorTitle = 'خطأ في الاتصال';
            errorMessage = 'فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى.';
        } else if (error.message.includes('400')) {
            errorTitle = 'خطأ في البيانات';
            errorMessage = 'البيانات المرسلة غير صحيحة. يرجى مراجعة المحتوى والمحاولة مرة أخرى.';
        } else if (error.message.includes('401') || error.message.includes('403')) {
            errorTitle = 'خطأ في الصلاحيات';
            errorMessage = 'ليس لديك صلاحية لنشر الصفحة. يرجى تسجيل الدخول مرة أخرى.';
        } else if (error.message.includes('500')) {
            errorTitle = 'خطأ في الخادم';
            errorMessage = 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً أو التواصل مع الدعم الفني.';
        } else {
            errorMessage = `حدث خطأ أثناء نشر الصفحة الرئيسية:\n\n${error.message}\n\nيرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.`;
        }

        await showAlert(errorTitle, errorMessage, 'error');

        // حفظ معلومات الخطأ للمراجعة
        localStorage.setItem('last_publish_error', JSON.stringify({
            error: error.message,
            timestamp: new Date().toISOString(),
            time: errorTime,
            backupCreated: backupCreated
        }));

        // إرسال تقرير الخطأ للتحليل
        reportError('publish_error', error, {
            time: errorTime,
            context: 'homepage_publish',
            userAction: 'publish_homepage',
            backupStatus: backupCreated
        });
    }
}

/**
 * دالة التراجع المحسنة والمطورة
 * تدير عمليات التراجع مع حفظ الحالة وإدارة الذاكرة
 *
 * الميزات المحسنة:
 * - إدارة ذكية لمكدس التراجع (50 حالة كحد أقصى)
 * - حفظ تلقائي للحالة الحالية
 * - معالجة شاملة للأخطاء
 * - إشعارات بصرية محسنة
 * - تتبع الأداء والإحصائيات
 * - حماية من فقدان البيانات
 *
 * @returns {Promise<void>}
 */
async function undoAction() {
    const startTime = performance.now();

    try {
        // تسجيل بداية العملية
        console.log('↶ بدء عملية التراجع...');
        updateLastActivity();

        // التحقق من وجود حالات للتراجع
        if (undoStack.length <= 1) {
            await showAlert(
                'لا توجد عمليات للتراجع',
                'لا توجد إجراءات سابقة يمكن التراجع عنها.',
                'info'
            );
            console.log('ℹ️ لا توجد حالات للتراجع');
            return;
        }

        // عرض مؤشر التحميل
        showLoadingIndicator('جاري التراجع عن آخر عملية...', 'undo');

        // حفظ الحالة الحالية في مكدس الإعادة قبل التراجع
        const currentState = JSON.stringify(homepageData);
        redoStack.push(currentState);

        // إزالة الحالة الحالية من مكدس التراجع
        undoStack.pop();

        // استرجاع الحالة السابقة
        const previousState = undoStack[undoStack.length - 1];
        // التحقق من صحة البيانات المسترجعة
        if (!previousState) {
            throw new Error('البيانات المسترجعة غير صحيحة');
        }

        // استرجاع البيانات مع معالجة الأخطاء
        try {
            homepageData = JSON.parse(previousState);
        } catch (parseError) {
            throw new Error('فشل في تحليل البيانات المسترجعة');
        }

        // إعادة رسم الصفحة مع معالجة الأخطاء
        try {
            if (typeof renderHomepage === 'function') {
                await renderHomepage();
            } else {
                console.warn('⚠️ دالة renderHomepage غير متاحة');
            }
        } catch (renderError) {
            console.warn('⚠️ خطأ في إعادة رسم الصفحة:', renderError.message);
        }

        // تحديث حالة النظام
        updateSaveIndicator('unsaved');
        hasUnsavedChanges = true;
        currentStateIndex = undoStack.length - 1;

        // إدارة ذكية لمكدس الإعادة (الحد الأقصى 20 عنصر)
        if (redoStack.length > 20) {
            redoStack = redoStack.slice(-20);
        }

        // حساب وقت الأداء
        const endTime = performance.now();
        const operationTime = endTime - startTime;
        performanceMetrics.lastOperation = 'undo';

        hideLoadingIndicator();

        console.log(`✅ تم التراجع عن آخر إجراء في ${Math.round(operationTime)}ms`);
        console.log(`📊 حالات التراجع المتاحة: ${undoStack.length - 1}, حالات الإعادة: ${redoStack.length}`);

        // إشعار بصري محسن
        showToast(`تم التراجع بنجاح (${undoStack.length - 1} حالات متبقية)`, 'info');

    } catch (error) {
        // معالجة شاملة للأخطاء
        const endTime = performance.now();
        const errorTime = endTime - startTime;

        console.error('❌ خطأ في التراجع:', {
            message: error.message,
            stack: error.stack,
            time: errorTime,
            undoStackLength: undoStack.length,
            redoStackLength: redoStack.length
        });

        hideLoadingIndicator();

        await showAlert(
            'خطأ في التراجع',
            `حدث خطأ أثناء التراجع:\n\n${error.message}\n\nيرجى المحاولة مرة أخرى أو إعادة تحميل الصفحة.`,
            'error'
        );

        // إرسال تقرير الخطأ
        reportError('undo_error', error, {
            time: errorTime,
            context: 'homepage_undo',
            stackSizes: {
                undo: undoStack.length,
                redo: redoStack.length
            }
        });
    }
}

/**
 * دالة الإعادة المحسنة والمطورة
 * تدير عمليات الإعادة مع حفظ الحالة وإدارة الذاكرة
 *
 * الميزات المحسنة:
 * - إدارة ذكية لمكدس الإعادة
 * - حفظ تلقائي للحالة الحالية
 * - معالجة شاملة للأخطاء
 * - إشعارات بصرية محسنة
 * - تتبع الأداء والإحصائيات
 * - حماية من فقدان البيانات
 *
 * @returns {Promise<void>}
 */
async function redoAction() {
    const startTime = performance.now();

    try {
        // تسجيل بداية العملية
        console.log('↷ بدء عملية الإعادة...');
        updateLastActivity();

        // التحقق من وجود حالات للإعادة
        if (redoStack.length === 0) {
            await showAlert(
                'لا توجد عمليات للإعادة',
                'لا توجد إجراءات يمكن إعادتها.',
                'info'
            );
            console.log('ℹ️ لا توجد حالات للإعادة');
            return;
        }

        // عرض مؤشر التحميل
        showLoadingIndicator('جاري إعادة آخر عملية تم التراجع عنها...', 'redo');

        // حفظ الحالة الحالية في مكدس التراجع
        const currentState = JSON.stringify(homepageData);
        undoStack.push(currentState);

        // استرجاع الحالة التالية من مكدس الإعادة
        const nextState = redoStack.pop();

        // التحقق من صحة البيانات المسترجعة
        if (!nextState) {
            throw new Error('البيانات المسترجعة غير صحيحة');
        }

        // استرجاع البيانات مع معالجة الأخطاء
        try {
            homepageData = JSON.parse(nextState);
        } catch (parseError) {
            throw new Error('فشل في تحليل البيانات المسترجعة');
        }

        // إعادة رسم الصفحة مع معالجة الأخطاء
        try {
            if (typeof renderHomepage === 'function') {
                await renderHomepage();
            } else {
                console.warn('⚠️ دالة renderHomepage غير متاحة');
            }
        } catch (renderError) {
            console.warn('⚠️ خطأ في إعادة رسم الصفحة:', renderError.message);
        }

        // تحديث حالة النظام
        updateSaveIndicator('unsaved');
        hasUnsavedChanges = true;
        currentStateIndex = undoStack.length - 1;

        // إدارة ذكية لمكدس التراجع (الحد الأقصى 50 عنصر)
        if (undoStack.length > HOMEPAGE_CONFIG.maxUndoSteps) {
            undoStack = undoStack.slice(-HOMEPAGE_CONFIG.maxUndoSteps);
        }

        // حساب وقت الأداء
        const endTime = performance.now();
        const operationTime = endTime - startTime;
        performanceMetrics.lastOperation = 'redo';

        hideLoadingIndicator();

        console.log(`✅ تم إعادة آخر إجراء في ${Math.round(operationTime)}ms`);
        console.log(`📊 حالات التراجع المتاحة: ${undoStack.length - 1}, حالات الإعادة: ${redoStack.length}`);

        // إشعار بصري محسن
        showToast(`تم الإعادة بنجاح (${redoStack.length} حالات متبقية)`, 'success');

    } catch (error) {
        // معالجة شاملة للأخطاء
        const endTime = performance.now();
        const errorTime = endTime - startTime;

        console.error('❌ خطأ في الإعادة:', {
            message: error.message,
            stack: error.stack,
            time: errorTime,
            undoStackLength: undoStack.length,
            redoStackLength: redoStack.length
        });

        hideLoadingIndicator();

        await showAlert(
            'خطأ في الإعادة',
            `حدث خطأ أثناء الإعادة:\n\n${error.message}\n\nيرجى المحاولة مرة أخرى أو إعادة تحميل الصفحة.`,
            'error'
        );

        // إرسال تقرير الخطأ
        reportError('redo_error', error, {
            time: errorTime,
            context: 'homepage_redo',
            stackSizes: {
                undo: undoStack.length,
                redo: redoStack.length
            }
        });
    }
}

/**
 * دوال مساعدة محسنة للنظام
 */

// دالة التحقق من صحة البيانات
async function validateHomepageData() {
    const errors = [];

    if (!homepageData || typeof homepageData !== 'object') {
        return { isValid: false, message: 'بيانات الصفحة غير صحيحة', errors: ['بيانات غير صحيحة'] };
    }

    if (!homepageData.sections || !Array.isArray(homepageData.sections)) {
        errors.push('لا توجد أقسام في الصفحة');
    }

    if (homepageData.sections && homepageData.sections.length === 0) {
        return { isValid: false, message: 'لا يوجد محتوى للمعاينة', errors: ['صفحة فارغة'] };
    }

    return { isValid: errors.length === 0, message: 'البيانات صحيحة', errors };
}

// دالة التحقق من البيانات للنشر
async function validateHomepageDataForPublishing() {
    const basicValidation = await validateHomepageData();
    if (!basicValidation.isValid) {
        return basicValidation;
    }

    const errors = [];

    // التحقق من وجود عنوان
    if (!homepageData.metadata?.title || homepageData.metadata.title.trim() === '') {
        errors.push('يجب إضافة عنوان للصفحة');
    }

    // التحقق من وجود وصف
    if (!homepageData.metadata?.description || homepageData.metadata.description.trim() === '') {
        errors.push('يجب إضافة وصف للصفحة');
    }

    return { isValid: errors.length === 0, message: 'البيانات جاهزة للنشر', errors };
}

// دالة إنشاء محتوى افتراضي للمعاينة
async function generateDefaultPreviewContent() {
    return `
        <div class="academy-preview-placeholder" style="padding: 40px; text-align: center; background: linear-gradient(135deg, #8B4513, #D2691E); color: white; border-radius: 10px; margin: 20px;">
            <h2 style="margin-bottom: 20px; font-family: 'Cairo', sans-serif;">أكاديمية 7C الرياضية</h2>
            <p style="font-size: 18px; margin-bottom: 15px;">مرحباً بكم في الصفحة الرئيسية</p>
            <p style="opacity: 0.8;">لا يوجد محتوى للعرض حالياً. يرجى إضافة عناصر إلى الصفحة.</p>
        </div>
    `;
}

// دالة إنشاء HTML متقدم للمعاينة
async function generateAdvancedPreviewHTML(content) {
    const timestamp = new Date().toLocaleString('ar-SA');

    return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة الصفحة الرئيسية - أكاديمية 7C الرياضية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
            line-height: 1.6;
            direction: rtl;
        }

        .preview-header {
            background: linear-gradient(90deg, #8B4513, #D2691E);
            padding: 15px 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .preview-content {
            padding: 20px;
            min-height: calc(100vh - 120px);
        }

        .preview-footer {
            background: #1a1a1a;
            padding: 10px 20px;
            text-align: center;
            font-size: 12px;
            opacity: 0.7;
            border-top: 1px solid #333;
        }

        .academy-logo {
            display: inline-block;
            font-size: 24px;
            font-weight: 700;
            color: #ffffff;
            text-decoration: none;
        }

        .preview-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            margin-top: 5px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="preview-header">
        <div class="academy-logo">🏆 أكاديمية 7C الرياضية</div>
        <div class="preview-badge">معاينة الصفحة الرئيسية</div>
    </div>

    <div class="preview-content">
        ${content}
    </div>

    <div class="preview-footer">
        تم إنشاء المعاينة في: ${timestamp} | أكاديمية 7C الرياضية © 2024
    </div>
</body>
</html>
    `;
}

// دالة إعداد معالجات الأحداث المتقدمة للمعاينة
async function setupAdvancedPreviewEventHandlers() {
    if (!previewWindow || previewWindow.closed) return;

    try {
        // إضافة معالج لإغلاق النافذة
        previewWindow.addEventListener('beforeunload', () => {
            console.log('🔄 تم إغلاق نافذة المعاينة');
        });

        // إضافة اختصارات لوحة المفاتيح
        previewWindow.document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                // إعادة تحميل المعاينة
                previewHomepage();
            }
        });

    } catch (error) {
        console.warn('⚠️ تعذر إعداد معالجات الأحداث للمعاينة:', error.message);
    }
}

// دالة تطبيق التحسينات البصرية
async function applyPreviewEnhancements() {
    if (!previewWindow || previewWindow.closed) return;

    try {
        // إضافة تأثيرات بصرية
        const style = previewWindow.document.createElement('style');
        style.textContent = `
            .preview-content * {
                transition: all 0.3s ease;
            }

            .preview-content *:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(139, 69, 19, 0.3);
            }
        `;
        previewWindow.document.head.appendChild(style);

    } catch (error) {
        console.warn('⚠️ تعذر تطبيق التحسينات البصرية:', error.message);
    }
}

/**
 * دالة الحفظ الأساسية (للتوافق مع الكود الموجود)
 */
async function saveHomepage() {
    return await saveHomepageEnhanced();
}

/**
 * دالة الحفظ المحسنة مع قاعدة البيانات
 */
async function saveHomepageEnhanced() {
    const startTime = performance.now();

    try {
        console.log('💾 بدء عملية الحفظ المحسنة...');
        showLoadingIndicator('جاري حفظ البيانات في قاعدة البيانات...', 'save');

        // تحديث وقت الحفظ والبيانات الوصفية
        homepageData.lastSaved = new Date().toISOString();
        homepageData.updatedAt = new Date().toISOString();
        homepageData.version = generateNewVersion();

        // حفظ في localStorage أولاً كنسخة احتياطية
        localStorage.setItem('homepage_data', JSON.stringify(homepageData));
        localStorage.setItem('homepage_backup', JSON.stringify({
            data: homepageData,
            timestamp: Date.now(),
            version: homepageData.version
        }));

        // إرسال إلى قاعدة البيانات مع معالجة متقدمة
        const response = await fetch(HOMEPAGE_CONFIG.apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=utf-8',
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                action: 'save_homepage',
                data: homepageData,
                version: homepageData.version,
                status: 'draft',
                metadata: {
                    timestamp: Date.now(),
                    userAgent: navigator.userAgent,
                    sessionId: sessionId
                }
            })
        });

        if (!response.ok) {
            throw new Error(`خطأ في الخادم (${response.status})`);
        }

        const result = await response.json();

        if (result.success) {
            // تحديث حالة النظام
            hasUnsavedChanges = false;
            updateSaveIndicator('saved');

            // حساب وقت الأداء
            const endTime = performance.now();
            performanceMetrics.saveTime = endTime - startTime;

            // تحديث معلومات آخر حفظ
            const lastSaveElement = document.getElementById('last-save-time');
            if (lastSaveElement) {
                lastSaveElement.textContent = new Date().toLocaleTimeString('ar-SA');
            }

            hideLoadingIndicator();
            
            console.log('✅ تم حفظ البيانات بنجاح:', result.data);
            
            showToast('تم حفظ التغييرات بنجاح', 'success');
            
        } else {
            throw new Error(result.message || 'فشل في حفظ البيانات');
        }

    } catch (error) {
        console.error('❌ خطأ في حفظ البيانات:', error);
        hideLoadingIndicator();
        
        // الحفظ في localStorage كنسخة احتياطية
        try {
            localStorage.setItem('homepage_data_backup', JSON.stringify(homepageData));
            await showAlert(
                'تحذير',
                'فشل الحفظ في قاعدة البيانات، تم حفظ نسخة احتياطية محلياً.\nالخطأ: ' + error.message,
                'warning'
            );
        } catch (localError) {
            await showAlert('خطأ', 'فشل في حفظ البيانات: ' + error.message, 'error');
        }
    }
}

/**
 * دالة رسم الصفحة الرئيسية المحسنة
 */
async function renderHomepage() {
    try {
        const canvas = document.getElementById('page-canvas');
        const emptyCanvas = document.getElementById('empty-canvas');

        if (!canvas) {
            console.warn('⚠️ عنصر page-canvas غير موجود');
            return;
        }

        // مسح المحتوى الحالي
        canvas.innerHTML = '';

        if (!homepageData.sections || homepageData.sections.length === 0) {
            // عرض رسالة الصفحة الفارغة
            if (emptyCanvas) {
                canvas.appendChild(emptyCanvas);
                emptyCanvas.style.display = 'block';
            }
        } else {
            // إخفاء رسالة الصفحة الفارغة
            if (emptyCanvas) {
                emptyCanvas.style.display = 'none';
            }

            // رسم العناصر
            homepageData.sections.forEach(sectionData => {
                const elementDOM = createElementDOM(sectionData);
                canvas.appendChild(elementDOM);
            });
        }

        console.log('✅ تم رسم الصفحة الرئيسية بنجاح');

    } catch (error) {
        console.error('❌ خطأ في رسم الصفحة:', error);
        await showAlert('خطأ', 'حدث خطأ أثناء رسم الصفحة: ' + error.message, 'error');
    }
}

/**
 * دوال مساعدة محسنة
 */

// إنشاء HTML للمعاينة
function generatePreviewHTML() {
    const canvas = document.getElementById('page-canvas');
    const canvasContent = canvas ? canvas.innerHTML : '<p style="text-align: center; color: #666;">لا يوجد محتوى للمعاينة</p>';
    
    return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>معاينة الصفحة الرئيسية - أكاديمية 7C الرياضية</title>
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body { 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, ${HOMEPAGE_CONFIG.colors.primary}, ${HOMEPAGE_CONFIG.colors.secondary});
                    color: white;
                    min-height: 100vh;
                    padding: 20px;
                }
                .preview-header {
                    background: rgba(255,255,255,0.1);
                    padding: 15px;
                    border-radius: 10px;
                    margin-bottom: 20px;
                    text-align: center;
                    backdrop-filter: blur(10px);
                }
                .preview-content {
                    background: rgba(255,255,255,0.05);
                    border-radius: 15px;
                    padding: 20px;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.1);
                }
                .element { margin: 10px 0; padding: 15px; border-radius: 8px; }
                .close-btn {
                    position: fixed;
                    top: 20px;
                    left: 20px;
                    background: ${HOMEPAGE_CONFIG.colors.error};
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    cursor: pointer;
                    z-index: 1000;
                }
            </style>
        </head>
        <body>
            <button class="close-btn" onclick="window.close()">✕ إغلاق المعاينة</button>
            <div class="preview-header">
                <h1>🏠 معاينة الصفحة الرئيسية</h1>
                <p>أكاديمية 7C الرياضية - ${new Date().toLocaleString('ar-SA')}</p>
            </div>
            <div class="preview-content">
                ${canvasContent}
            </div>
        </body>
        </html>
    `;
}

// حفظ الحالة الحالية
function saveState() {
    const state = JSON.stringify(homepageData);
    undoStack.push(state);

    // الحد الأقصى للخطوات
    if (undoStack.length > HOMEPAGE_CONFIG.maxUndoSteps) {
        undoStack.shift();
    }

    // مسح مكدس الإعادة عند حفظ حالة جديدة
    redoStack = [];
}

// تحديث مؤشر الحفظ
function updateSaveIndicator(status) {
    const indicator = document.getElementById('save-indicator');
    if (!indicator) return;

    indicator.className = 'save-indicator';

    switch (status) {
        case 'saved':
            indicator.classList.add('bg-green-500');
            indicator.textContent = 'محفوظ';
            break;
        case 'unsaved':
            indicator.classList.add('bg-orange-500');
            indicator.textContent = 'غير محفوظ';
            hasUnsavedChanges = true;
            break;
        case 'saving':
            indicator.classList.add('bg-blue-500');
            indicator.textContent = 'جاري الحفظ...';
            break;
        case 'published':
            indicator.classList.add('bg-purple-500');
            indicator.textContent = 'منشور';
            break;
        case 'error':
            indicator.classList.add('bg-red-500');
            indicator.textContent = 'خطأ';
            break;
    }
}

/**
 * دوال الواجهة والتفاعل المحسنة
 */

// عرض مؤشر التحميل
function showLoadingIndicator(message = 'جاري التحميل...') {
    const existingLoader = document.getElementById('loading-indicator');
    if (existingLoader) {
        existingLoader.remove();
    }

    const loader = document.createElement('div');
    loader.id = 'loading-indicator';
    loader.innerHTML = `
        <div style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(5px);
        ">
            <div style="
                background: white;
                padding: 30px;
                border-radius: 15px;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                color: ${HOMEPAGE_CONFIG.colors.primary};
            ">
                <div style="
                    width: 40px;
                    height: 40px;
                    border: 4px solid ${HOMEPAGE_CONFIG.colors.secondary};
                    border-top: 4px solid ${HOMEPAGE_CONFIG.colors.accent};
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 15px;
                "></div>
                <p style="margin: 0; font-weight: bold;">${message}</p>
            </div>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;

    document.body.appendChild(loader);
}

// إخفاء مؤشر التحميل
function hideLoadingIndicator() {
    const loader = document.getElementById('loading-indicator');
    if (loader) {
        loader.remove();
    }
}

// عرض رسائل SweetAlert2 محسنة
async function showAlert(title, text, icon = 'info') {
    if (typeof Swal === 'undefined') {
        alert(`${title}\n${text}`);
        return;
    }

    return await Swal.fire({
        title: title,
        text: text,
        icon: icon,
        confirmButtonText: 'موافق',
        background: 'var(--glass-strong, #1a1a1a)',
        color: 'var(--text-primary, white)',
        confirmButtonColor: HOMEPAGE_CONFIG.colors.secondary,
        customClass: {
            popup: 'rtl-popup',
            title: 'rtl-title',
            content: 'rtl-content'
        }
    });
}

// عرض حوار التأكيد
async function showConfirmDialog(title, text, icon = 'question') {
    if (typeof Swal === 'undefined') {
        return { isConfirmed: confirm(`${title}\n${text}`) };
    }

    return await Swal.fire({
        title: title,
        text: text,
        icon: icon,
        showCancelButton: true,
        confirmButtonText: 'نعم',
        cancelButtonText: 'إلغاء',
        background: 'var(--glass-strong, #1a1a1a)',
        color: 'var(--text-primary, white)',
        confirmButtonColor: HOMEPAGE_CONFIG.colors.secondary,
        cancelButtonColor: HOMEPAGE_CONFIG.colors.error,
        customClass: {
            popup: 'rtl-popup',
            title: 'rtl-title',
            content: 'rtl-content'
        }
    });
}

// عرض إشعار سريع (Toast)
function showToast(message, type = 'info', duration = 3000) {
    const existingToast = document.getElementById('toast-notification');
    if (existingToast) {
        existingToast.remove();
    }

    const colors = {
        success: HOMEPAGE_CONFIG.colors.success,
        error: HOMEPAGE_CONFIG.colors.error,
        warning: HOMEPAGE_CONFIG.colors.warning,
        info: HOMEPAGE_CONFIG.colors.secondary
    };

    const toast = document.createElement('div');
    toast.id = 'toast-notification';
    toast.innerHTML = `
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${colors[type] || colors.info};
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            z-index: 10000;
            animation: slideIn 0.3s ease-out;
            max-width: 300px;
            direction: rtl;
        ">
            ${message}
        </div>
        <style>
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        </style>
    `;

    document.body.appendChild(toast);

    // إزالة الإشعار تلقائياً
    setTimeout(() => {
        if (toast) {
            toast.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => toast.remove(), 300);
        }
    }, duration);
}

/**
 * تحميل البيانات من قاعدة البيانات
 */
async function loadHomepageData() {
    try {
        showLoadingIndicator('جاري تحميل بيانات الصفحة الرئيسية...');

        const response = await fetch(`${HOMEPAGE_CONFIG.apiUrl}?action=get_homepage`);
        const result = await response.json();

        if (result.success && result.data.homepage) {
            homepageData = result.data.homepage;

            // تحديث معلومات آخر حفظ
            if (result.data.last_updated) {
                const lastSaveElement = document.getElementById('last-save-time');
                if (lastSaveElement) {
                    const saveDate = new Date(result.data.last_updated);
                    lastSaveElement.textContent = saveDate.toLocaleTimeString('ar-SA');
                }
            }

            await renderHomepage();
            updateSaveIndicator('saved');
            hasUnsavedChanges = false;

            console.log('✅ تم تحميل بيانات الصفحة الرئيسية من قاعدة البيانات');

        } else {
            // تحميل من localStorage كنسخة احتياطية
            const localData = localStorage.getItem('homepage_data');
            if (localData) {
                homepageData = JSON.parse(localData);
                await renderHomepage();
                console.log('⚠️ تم تحميل البيانات من التخزين المحلي');
                showToast('تم تحميل البيانات من التخزين المحلي', 'warning');
            }
        }

        hideLoadingIndicator();

    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
        hideLoadingIndicator();

        // محاولة التحميل من localStorage
        try {
            const localData = localStorage.getItem('homepage_data');
            if (localData) {
                homepageData = JSON.parse(localData);
                await renderHomepage();
                showToast('تم تحميل البيانات من التخزين المحلي', 'warning');
            }
        } catch (localError) {
            await showAlert('خطأ', 'فشل في تحميل بيانات الصفحة الرئيسية', 'error');
        }
    }
}

/**
 * الحفظ التلقائي المحسن
 */
function setupAutoSave() {
    setInterval(async () => {
        if (hasUnsavedChanges && !isAutoSaving) {
            isAutoSaving = true;
            try {
                await saveHomepage();
                console.log('💾 تم الحفظ التلقائي');
            } catch (error) {
                console.error('❌ خطأ في الحفظ التلقائي:', error);
            } finally {
                isAutoSaving = false;
            }
        }
    }, HOMEPAGE_CONFIG.autoSaveInterval);
}

/**
 * إعداد اختصارات لوحة المفاتيح المحسنة
 */
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+Z - التراجع
        if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
            e.preventDefault();
            undoAction();
        }

        // Ctrl+Y أو Ctrl+Shift+Z - الإعادة
        if (e.ctrlKey && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {
            e.preventDefault();
            redoAction();
        }

        // Ctrl+S - الحفظ
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveHomepage();
        }

        // Ctrl+P - المعاينة
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            previewHomepage();
        }

        // Ctrl+Shift+P - النشر
        if (e.ctrlKey && e.shiftKey && e.key === 'P') {
            e.preventDefault();
            publishHomepage();
        }
    });
}

/**
 * تهيئة النظام عند تحميل الصفحة
 */
document.addEventListener('DOMContentLoaded', async function() {
    try {
        console.log('🚀 بدء تهيئة نظام إدارة الصفحة الرئيسية المحسن...');

        await loadHomepageData();
        setupKeyboardShortcuts();
        setupAutoSave();
        updateSaveIndicator('saved');

        // حفظ الحالة الأولية
        saveState();

        console.log('✅ تم تحميل نظام إدارة الصفحة الرئيسية المحسن بالكامل');
        showToast('تم تحميل النظام بنجاح', 'success');

    } catch (error) {
        console.error('❌ خطأ في تهيئة النظام:', error);
        showToast('خطأ في تهيئة النظام', 'error');
    }
});

// منع فقدان البيانات عند إغلاق الصفحة
window.addEventListener('beforeunload', function(e) {
    if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = 'يوجد تغييرات غير محفوظة. هل أنت متأكد من الخروج؟';
        return e.returnValue;
    }
});

/**
 * دوال مساعدة إضافية للنظام المحسن
 */

// دالة إنشاء معلومات النشر
async function generatePublishInfo() {
    const sectionsCount = homepageData?.sections?.length || 0;
    const lastModified = homepageData?.lastSaved ?
        new Date(homepageData.lastSaved).toLocaleString('ar-SA') : 'غير محدد';

    return `📊 عدد الأقسام: ${sectionsCount}\n⏰ آخر تعديل: ${lastModified}\n📝 الحالة: مسودة`;
}

// دالة إنشاء إصدار جديد
function generateNewVersion() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');

    return `v${year}.${month}.${day}.${hour}${minute}`;
}

// دالة إنشاء نسخة احتياطية قبل النشر
async function createBackupBeforePublish() {
    try {
        const backupData = {
            data: JSON.parse(JSON.stringify(homepageData)),
            timestamp: new Date().toISOString(),
            version: homepageData.version || generateNewVersion(),
            type: 'pre_publish_backup'
        };

        // حفظ في localStorage
        localStorage.setItem('homepage_backup_pre_publish', JSON.stringify(backupData));

        // إرسال إلى الخادم
        const response = await fetch(HOMEPAGE_CONFIG.apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=utf-8'
            },
            body: JSON.stringify({
                action: 'create_backup',
                data: backupData
            })
        });

        if (!response.ok) {
            throw new Error('فشل في إنشاء النسخة الاحتياطية على الخادم');
        }

        console.log('✅ تم إنشاء نسخة احتياطية بنجاح');
        return true;

    } catch (error) {
        console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
        throw error;
    }
}

// دالة الحصول على رسالة خطأ مفصلة
function getDetailedErrorMessage(error, context = '') {
    let message = 'حدث خطأ غير متوقع';

    if (error.name === 'TypeError') {
        message = 'خطأ في نوع البيانات أو الوصول إلى خاصية غير موجودة';
    } else if (error.name === 'ReferenceError') {
        message = 'خطأ في مرجع متغير أو دالة غير معرفة';
    } else if (error.name === 'SyntaxError') {
        message = 'خطأ في بناء الجملة أو تحليل البيانات';
    } else if (error.message) {
        message = error.message;
    }

    if (context) {
        message = `${context}: ${message}`;
    }

    return message;
}

// دالة إرسال تقرير الخطأ
function reportError(errorType, error, additionalData = {}) {
    try {
        const errorReport = {
            type: errorType,
            message: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            additionalData: additionalData
        };

        // حفظ في localStorage للمراجعة
        const existingErrors = JSON.parse(localStorage.getItem('error_reports') || '[]');
        existingErrors.push(errorReport);

        // الاحتفاظ بآخر 10 أخطاء فقط
        if (existingErrors.length > 10) {
            existingErrors.splice(0, existingErrors.length - 10);
        }

        localStorage.setItem('error_reports', JSON.stringify(existingErrors));

        console.log('📊 تم حفظ تقرير الخطأ:', errorReport);

    } catch (reportError) {
        console.error('❌ فشل في إرسال تقرير الخطأ:', reportError);
    }
}

// دالة تحديث آخر نشاط
function updateLastActivity() {
    systemVariables.lastActivity = Date.now();
    localStorage.setItem('last_activity', systemVariables.lastActivity.toString());
}

// دالة عرض مؤشر التحميل المحسن
function showLoadingIndicator(message = 'جاري التحميل...', type = 'default') {
    const indicator = document.getElementById('loading-indicator');
    if (indicator) {
        indicator.style.display = 'flex';
        indicator.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner ${type}"></div>
                <div class="loading-message">${message}</div>
            </div>
        `;
    }
}

// دالة إخفاء مؤشر التحميل
function hideLoadingIndicator() {
    const indicator = document.getElementById('loading-indicator');
    if (indicator) {
        indicator.style.display = 'none';
    }
}

// دالة عرض الإشعارات المحسنة
function showToast(message, type = 'info', duration = 3000) {
    // إنشاء عنصر الإشعار
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <span class="toast-icon">${getToastIcon(type)}</span>
            <span class="toast-message">${message}</span>
        </div>
    `;

    // إضافة الأنماط
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${getToastColor(type)};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        font-family: 'Cairo', sans-serif;
        font-size: 14px;
        max-width: 300px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    document.body.appendChild(toast);

    // تحريك الإشعار للداخل
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    // إزالة الإشعار
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, duration);
}

// دالة الحصول على أيقونة الإشعار
function getToastIcon(type) {
    const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
    };
    return icons[type] || icons.info;
}

// دالة الحصول على لون الإشعار
function getToastColor(type) {
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };
    return colors[type] || colors.info;
}
